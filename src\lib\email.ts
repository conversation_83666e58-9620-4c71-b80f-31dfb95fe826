import nodemailer from 'nodemailer';

// Email configuration
const EMAIL_CONFIG = {
  service: 'gmail',
  host: 'smtp.gmail.com',
  port: 587,
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.GMAIL_USER, // Your Gmail address
    pass: process.env.GMAIL_APP_PASSWORD, // Your Gmail App Password
  },
};

// Create transporter
let transporter: nodemailer.Transporter | null = null;

function getTransporter() {
  if (!transporter) {
    if (!process.env.GMAIL_USER || !process.env.GMAIL_APP_PASSWORD) {
      throw new Error('Gmail credentials not configured. Please set GMAIL_USER and GMAIL_APP_PASSWORD environment variables.');
    }
    
    transporter = nodemailer.createTransporter(EMAIL_CONFIG);
  }
  return transporter;
}

/**
 * Generate a random 6-digit verification code
 */
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * Send verification email
 */
export async function sendVerificationEmail(
  email: string,
  code: string,
  type: 'signup' | 'signin' | 'password-reset',
  username?: string
): Promise<void> {
  try {
    const transporter = getTransporter();
    
    const subject = type === 'signup'
      ? 'LDIS - Email Verification Code'
      : type === 'signin'
      ? 'LDIS - Sign In Verification Code'
      : 'LDIS - Password Reset Verification Code';
    
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${subject}</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              background-color: #f5f5f5;
            }
            .container {
              background-color: white;
              padding: 40px;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
            }
            .logo {
              font-size: 24px;
              font-weight: bold;
              color: oklch(0.5 0.15 240);
              margin-bottom: 10px;
            }
            .code-container {
              background-color: #f8f9fa;
              border: 2px solid oklch(0.5 0.15 240);
              border-radius: 8px;
              padding: 20px;
              text-align: center;
              margin: 30px 0;
            }
            .verification-code {
              font-size: 32px;
              font-weight: bold;
              letter-spacing: 4px;
              color: oklch(0.5 0.15 240);
              font-family: 'Courier New', monospace;
            }
            .footer {
              margin-top: 30px;
              padding-top: 20px;
              border-top: 1px solid #eee;
              font-size: 14px;
              color: #666;
              text-align: center;
            }
            .warning {
              background-color: #fff3cd;
              border: 1px solid #ffeaa7;
              border-radius: 4px;
              padding: 15px;
              margin: 20px 0;
              color: #856404;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">LDIS</div>
              <h1>${type === 'signup' ? 'Welcome to LDIS!' : type === 'signin' ? 'Sign In Verification' : 'Password Reset Verification'}</h1>
              <p>
                ${type === 'signup'
                  ? `Hi ${username || 'there'}! Please verify your email address to complete your registration.`
                  : type === 'signin'
                  ? `Hi ${username || 'there'}! Please verify your email address to sign in.`
                  : `Hi ${username || 'there'}! Please verify your email address to reset your password.`
                }
              </p>
            </div>
            
            <div class="code-container">
              <p><strong>Your verification code is:</strong></p>
              <div class="verification-code">${code}</div>
            </div>
            
            <div class="warning">
              <strong>Important:</strong>
              <ul>
                <li>This code will expire in 10 minutes</li>
                <li>Do not share this code with anyone</li>
                <li>If you didn't request this code, please ignore this email</li>
              </ul>
            </div>
            
            <p>
              Enter this code in the LDIS application to ${type === 'signup' ? 'complete your registration' : type === 'signin' ? 'sign in' : 'reset your password'}.
            </p>
            
            <div class="footer">
              <p>
                This email was sent from the Legal Document Issuance System (LDIS).<br>
                If you have any questions, please contact your system administrator.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;

    const textContent = `
LDIS - ${type === 'signup' ? 'Email Verification' : type === 'signin' ? 'Sign In Verification' : 'Password Reset Verification'}

${type === 'signup'
  ? `Hi ${username || 'there'}! Please verify your email address to complete your registration.`
  : type === 'signin'
  ? `Hi ${username || 'there'}! Please verify your email address to sign in.`
  : `Hi ${username || 'there'}! Please verify your email address to reset your password.`
}

Your verification code is: ${code}

Important:
- This code will expire in 10 minutes
- Do not share this code with anyone
- If you didn't request this code, please ignore this email

Enter this code in the LDIS application to ${type === 'signup' ? 'complete your registration' : type === 'signin' ? 'sign in' : 'reset your password'}.

---
This email was sent from the Legal Document Issuance System (LDIS).
If you have any questions, please contact your system administrator.
    `;

    const mailOptions = {
      from: {
        name: 'LDIS - Legal Document Issuance System',
        address: process.env.GMAIL_USER!,
      },
      to: email,
      subject: subject,
      text: textContent,
      html: htmlContent,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Verification email sent successfully:', info.messageId);
    
  } catch (error) {
    console.error('Error sending verification email:', error);
    throw new Error('Failed to send verification email');
  }
}

/**
 * Test email configuration
 */
export async function testEmailConfiguration(): Promise<boolean> {
  try {
    const transporter = getTransporter();
    await transporter.verify();
    console.log('Email configuration is valid');
    return true;
  } catch (error) {
    console.error('Email configuration test failed:', error);
    return false;
  }
}
